import 'package:flutter/material.dart';
import 'package:storetrack_app/config/themes/app_colors.dart';
import 'package:storetrack_app/core/extensions/theme_extensions.dart';

class EmptyState extends StatelessWidget {
  final String message;
  const EmptyState({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    // Get the screen height to create a container that fills most of the screen
    final screenHeight = MediaQuery.of(context).size.height;
    // Use 70% of screen height to ensure it's centered in the body
    final containerHeight = screenHeight;

    return Container(
      width: double.infinity,
      // height: containerHeight * 0.7,
      color: Colors.transparent,
      child: Column(
        children: [
          // const Spacer(flex: 3),
          Padding(
            padding: const EdgeInsets.all(0),
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.transparent,
                  border: Border.all(color: AppColors.blackTint2)),
              width: double.infinity,
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 20),
                child: Text(
                  message,
                  style: Theme.of(context).textTheme.montserratParagraphSmall,
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          // const Spacer(flex: 3),
        ],
      ),
    );
  }
}
